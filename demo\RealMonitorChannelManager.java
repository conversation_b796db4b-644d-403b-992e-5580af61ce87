package com.dahua.demo.basic;

import com.google.gson.Gson;
import java.util.ResourceBundle;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;
import java.io.*;
import java.net.URL;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 实时监控通道管理器
 * 融合了ComprehensiveOrgChannelManager.java、RealMonitor.java、BaseUserInfo.java、
 * KeepLogin.java和LoginOut.java的功能
 *
 * 主要功能：
 * 1. 使用SSL证书进行HTTPS安全连接
 * 2. 自动登录获取Token
 * 3. 自动会话保活管理（110秒间隔）
 * 4. 优雅的会话登出和资源清理
 * 5. 递归查询完整的组织机构树结构
 * 6. 查询每个组织包含的所有通道信息
 * 7. 为每个通道获取实时监控URI（支持RTSP、FLV_HTTP、HLS协议）
 * 8. 全程使用HTTPS访问，确保安全性
 * 9. 集成了基础用户信息配置功能
 * 10. 完整的会话生命周期管理
 */
public class RealMonitorChannelManager {

    // 从 BaseUserInfo 融合的配置信息
    protected static String ip;
    protected static String port;
    protected static String userName;
    protected static String password;
    protected static String token;

    static {
        ResourceBundle bundle = ResourceBundle.getBundle("baseinfo");
        ip = bundle.getString("ip");
        port = bundle.getString("port");
        userName = bundle.getString("userName");
        password = bundle.getString("password");
        //第一次获取到token之后保存到token.properties文件中，之后直接读取properties文件中token即可，防止重复申请token
        ResourceBundle tokenbundle = ResourceBundle.getBundle("token");
        if (!("").equals(tokenbundle.getString("token"))) {
            token = tokenbundle.getString("token");
        }else {
            System.out.println("没有进行登录，请调用Login的main方法进行登录");
        }
    }

    // API端点定义
    private static final String LOGIN_ACTION = "/videoService/accounts/authorize";
    private static final String ORG_TREE_ACTION = "/videoService/devicesManager/deviceTree";
    private static final String REAL_MONITOR_ACTION = "/videoService/realmonitor/uri";
    // 融合KeepLogin和LoginOut的API端点
    private static final String KEEP_ALIVE_ACTION = "/videoService/accounts/token/keepalive";
    private static final String LOGOUT_ACTION = "/videoService/accounts/unauthorize";
    
    // SSL上下文
    private static SSLContext sslContext = null;

    // 支持的协议类型
    private static final String[] SUPPORTED_SCHEMES = {"RTSP", "FLV_HTTP", "HLS"};

    // 会话管理相关变量（融合KeepLogin和LoginOut功能）
    private static volatile AtomicBoolean keepAliveRunning = new AtomicBoolean(false);
    private static Thread keepAliveThread = null;
    private static final long KEEP_ALIVE_INTERVAL = 110000; // 110秒，与KeepLogin保持一致
    private static volatile String currentToken = null;
    
    // 统计信息
    private static int totalOrganizations = 0;
    private static int totalChannels = 0;
    private static int totalMonitorUrls = 0;
    
    // 通道信息存储
    private static List<ChannelInfo> allChannels = new ArrayList<>();
    private static List<MonitorUrlInfo> allMonitorUrls = new ArrayList<>();
    
    // 基于OrgTree.getSub方法的层级显示变量
    public static int a = -1; // 机构层级标志
    public static String mark = ""; // 打印结果的空格标志
    
    /**
     * 通道信息类
     */
    public static class ChannelInfo {
        public String channelId;
        public String channelName;
        public String orgName;
        public String orgId;
        public int orgLevel;
        
        public ChannelInfo(String channelId, String channelName, String orgName, String orgId, int level) {
            this.channelId = channelId;
            this.channelName = channelName;
            this.orgName = orgName;
            this.orgId = orgId;
            this.orgLevel = level;
        }
        
        @Override
        public String toString() {
            return String.format("通道[%s] ID:%s 组织:%s", channelName, channelId, orgName);
        }
    }
    
    /**
     * 监控URL信息类
     */
    public static class MonitorUrlInfo {
        public String channelId;
        public String channelName;
        public String orgName;
        public String scheme;
        public String url;
        public int subType;
        
        public MonitorUrlInfo(String channelId, String channelName, String orgName, String scheme, String url, int subType) {
            this.channelId = channelId;
            this.channelName = channelName;
            this.orgName = orgName;
            this.scheme = scheme;
            this.url = url;
            this.subType = subType;
        }
        
        @Override
        public String toString() {
            return String.format("通道[%s] %s协议: %s", channelName, scheme, url);
        }
    }
    
    /**
     * 初始化SSL上下文，加载证书
     */
    private static synchronized void initSSLContext() throws Exception {
        if (sslContext != null) {
            return;
        }
        
        System.out.println("🔐 正在加载SSL证书...");
        
        // 创建证书工厂
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        
        // 加载根证书
        InputStream rootCertStream = RealMonitorChannelManager.class.getClassLoader()
                .getResourceAsStream("rootChain.crt");
        if (rootCertStream == null) {
            throw new Exception("找不到根证书文件: rootChain.crt");
        }
        Certificate rootCert = cf.generateCertificate(rootCertStream);
        rootCertStream.close();
        System.out.println("✅ 根证书加载成功");
        
        // 加载服务器证书
        InputStream serverCertStream = RealMonitorChannelManager.class.getClassLoader()
                .getResourceAsStream("key.cer");
        if (serverCertStream == null) {
            throw new Exception("找不到服务器证书文件: key.cer");
        }
        Certificate serverCert = cf.generateCertificate(serverCertStream);
        serverCertStream.close();
        System.out.println("✅ 服务器证书加载成功");
        
        // 创建KeyStore并添加证书
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);
        keyStore.setCertificateEntry("rootCA", rootCert);
        keyStore.setCertificateEntry("serverCert", serverCert);
        
        // 创建TrustManagerFactory
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(keyStore);
        
        // 创建SSLContext
        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), null);
        
        System.out.println("🔒 SSL上下文初始化完成");
    }
    
    /**
     * 发送安全HTTPS请求（支持GET和POST）
     */
    private static String sendSecureHttpsRequest(String method, String action, String token, String data) {
        try {
            initSSLContext();
            
            // 构建HTTPS URL
            String httpsUrl = "https://" + ip + ":" + port + action;
            if ("GET".equals(method) && data != null && !data.isEmpty()) {
                httpsUrl += data; // GET请求的参数直接拼接到URL
            }
            
            URL url = new URL(httpsUrl);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 设置SSL上下文
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            
            // 设置主机名验证器
            connection.setHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true; // 允许IP地址连接
                }
            });
            
            // 设置请求属性
            connection.setRequestMethod(method);
            connection.setRequestProperty("Content-Type", "application/json");
            if (token != null && !token.isEmpty()) {
                connection.setRequestProperty("X-Subject-Token", token);
            }
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            
            // 如果是POST请求且有数据，发送数据
            if ("POST".equals(method) && data != null && !data.isEmpty()) {
                connection.setDoOutput(true);
                OutputStream os = connection.getOutputStream();
                os.write(data.getBytes("UTF-8"));
                os.flush();
                os.close();
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            
            // 读取响应内容
            InputStream inputStream;
            if (responseCode >= 200 && responseCode < 300) {
                inputStream = connection.getInputStream();
            } else if (responseCode == 401 && "POST".equals(method)) {
                // 401对于第一次登录是正常的
                inputStream = connection.getErrorStream();
                if (inputStream == null) {
                    inputStream = connection.getInputStream();
                }
            } else {
                inputStream = connection.getErrorStream();
            }
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            return response.toString();
            
        } catch (Exception e) {
            System.err.println("❌ HTTPS请求失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * MD5加密
     */
    private static String md5(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hash = md.digest(input.getBytes("UTF-8"));
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
    
    /**
     * 计算MD5签名
     */
    private static String calculateSignature(String password, String realm, String randomKey) throws Exception {
        // 步骤1: MD5(password)
        String step1 = md5(password);
        
        // 步骤2: MD5(userName + step1)
        String step2 = md5(userName + step1);
        
        // 步骤3: MD5(step2)
        String step3 = md5(step2);
        
        // 步骤4: MD5(userName + ":" + realm + ":" + step3)
        String step4 = md5(userName + ":" + realm + ":" + step3);
        
        // 步骤5: MD5(step4 + ":" + randomKey)
        String signature = md5(step4 + ":" + randomKey);
        
        return signature;
    }

    /**
     * 执行安全HTTPS登录，获取Token
     */
    public static String performSecureLogin() throws Exception {
        System.out.println("🔑 开始安全SSL证书登录...");

        // 第一次登录
        Map<String, Object> loginFirst = new HashMap<>();
        loginFirst.put("clientType", "winpc");
        loginFirst.put("userName", userName);

        String firstData = new Gson().toJson(loginFirst);
        String firstResponse = sendSecureHttpsRequest("POST", LOGIN_ACTION, "", firstData);

        if (firstResponse == null || !firstResponse.startsWith("{")) {
            throw new Exception("第一次登录失败");
        }

        Map<String, Object> firstRsp = new Gson().fromJson(firstResponse, Map.class);
        String realm = (String) firstRsp.get("realm");
        String randomKey = (String) firstRsp.get("randomKey");

        if (realm == null || randomKey == null) {
            throw new Exception("未获取到realm或randomKey");
        }

        System.out.println("✅ 第一次登录成功，获取到认证信息");

        // 第二次登录
        Map<String, Object> loginSecond = new HashMap<>();
        loginSecond.put("clientType", "winpc");
        loginSecond.put("userName", userName);
        loginSecond.put("randomKey", randomKey);
        loginSecond.put("encryptType", "MD5");

        String signature = calculateSignature(password, realm, randomKey);
        loginSecond.put("signature", signature);

        String secondData = new Gson().toJson(loginSecond);
        String secondResponse = sendSecureHttpsRequest("POST", LOGIN_ACTION, "", secondData);

        if (secondResponse == null || !secondResponse.startsWith("{")) {
            throw new Exception("第二次登录失败");
        }

        Map<String, Object> secondRsp = new Gson().fromJson(secondResponse, Map.class);
        String token = (String) secondRsp.get("token");

        if (token == null || token.trim().isEmpty()) {
            throw new Exception("未获取到token");
        }

        System.out.println("✅ 登录成功，Token已获取");
        currentToken = token; // 保存当前token用于会话管理
        return token;
    }

    /**
     * 会话保活方法（融合KeepLogin.java功能）
     * 发送保活请求以维持会话有效性
     */
    public static String keepAlive(String token) throws Exception {
        if (token == null || token.trim().isEmpty()) {
            throw new Exception("Token不能为空");
        }

        String content = "{\"token\":\"" + token + "\"}";
        String response = sendSecureHttpsRequest("PUT", KEEP_ALIVE_ACTION, token, content);

        if (response == null) {
            throw new Exception("保活请求失败：收到空响应");
        }

        return response;
    }

    /**
     * 启动自动保活线程（融合KeepLogin.java的main方法功能）
     */
    public static void startKeepAlive(String token) {
        if (keepAliveRunning.get()) {
            System.out.println("⚠️ 保活线程已在运行中");
            return;
        }

        if (token == null || token.trim().isEmpty()) {
            System.err.println("❌ 无法启动保活：Token为空");
            return;
        }

        keepAliveRunning.set(true);
        currentToken = token;

        keepAliveThread = new Thread(() -> {
            System.out.println("🔄 启动自动会话保活线程（间隔：" + (KEEP_ALIVE_INTERVAL / 1000) + "秒）");

            while (keepAliveRunning.get()) {
                try {
                    String response = keepAlive(currentToken);
                    System.out.println("✅ 会话保活成功");

                    // 等待下次保活
                    Thread.sleep(KEEP_ALIVE_INTERVAL);
                } catch (InterruptedException e) {
                    System.out.println("🔄 保活线程被中断，正在停止...");
                    break;
                } catch (Exception e) {
                    System.err.println("❌ 会话保活失败: " + e.getMessage());
                    // 保活失败时等待较短时间后重试
                    try {
                        Thread.sleep(30000); // 30秒后重试
                    } catch (InterruptedException ie) {
                        break;
                    }
                }
            }

            System.out.println("🔄 自动会话保活线程已停止");
        });

        keepAliveThread.setDaemon(true); // 设置为守护线程
        keepAliveThread.setName("KeepAlive-Thread");
        keepAliveThread.start();
    }

    /**
     * 停止自动保活线程
     */
    public static void stopKeepAlive() {
        if (keepAliveRunning.get()) {
            System.out.println("🔄 正在停止会话保活线程...");
            keepAliveRunning.set(false);

            if (keepAliveThread != null && keepAliveThread.isAlive()) {
                keepAliveThread.interrupt();
                try {
                    keepAliveThread.join(5000); // 等待最多5秒
                } catch (InterruptedException e) {
                    System.err.println("⚠️ 等待保活线程停止时被中断");
                }
            }

            System.out.println("✅ 会话保活线程已停止");
        }
    }

    /**
     * 会话登出方法（融合LoginOut.java功能）
     * 销毁当前会话并清理token
     */
    public static String loginOut(String token) throws Exception {
        if (token == null || token.trim().isEmpty()) {
            throw new Exception("Token不能为空");
        }

        String content = "{\"token\":\"" + token + "\"}";
        String response = sendSecureHttpsRequest("POST", LOGOUT_ACTION, token, content);

        if (response != null) {
            System.out.println("✅ 会话登出响应: " + response);
        }

        return response;
    }

    /**
     * 清空token配置文件（融合LoginOut.java的tokenPro方法功能）
     */
    private static void clearTokenProperties() {
        Properties pro = new Properties();
        pro.setProperty("token", "");

        try {
            File file = new File("src/main/resources/token.properties");
            OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
            pro.store(writer, "clearToken");
            writer.close();
            System.out.println("✅ Token配置文件已清空");
        } catch (IOException e) {
            System.err.println("❌ 清空Token配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 执行完整的登出流程
     * 包括停止保活、登出会话、清空token
     */
    public static void performCompleteLogout() {
        try {
            System.out.println("🔄 开始执行完整登出流程...");

            // 1. 停止保活线程
            stopKeepAlive();

            // 2. 如果有token，执行登出
            if (currentToken != null && !currentToken.trim().isEmpty()) {
                try {
                    String response = loginOut(currentToken);
                    System.out.println("✅ 会话登出成功");
                } catch (Exception e) {
                    System.err.println("⚠️ 会话登出失败，但继续清理: " + e.getMessage());
                }
            }

            // 3. 清空token配置文件
            clearTokenProperties();

            // 4. 清空内存中的token
            currentToken = null;

            System.out.println("✅ 完整登出流程执行完成");

        } catch (Exception e) {
            System.err.println("❌ 登出流程执行失败: " + e.getMessage());
        }
    }

    /**
     * 添加JVM关闭钩子，确保程序异常退出时也能正确登出
     */
    private static void addShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\n🔄 检测到程序退出，执行清理操作...");
            performCompleteLogout();
        }, "Shutdown-Hook"));
    }

    /**
     * 基于OrgTree.getSub方法的递归获取组织机构树和通道信息
     * 此方法可以获取到当前环境所有的组织机构树，和每一个具体的机构所包含的通道
     */
    private static void getSub(String code, String token) throws Exception {
        String content = "?id=" + code + "&nodeType=1&typeCode=01&page=1&pageSize=100";
        String response = sendSecureHttpsRequest("GET", ORG_TREE_ACTION, token, content);

        if (response == null || !response.startsWith("{")) {
            System.err.println(mark + "❌ 获取组织信息失败");
            return;
        }

        try {
            List<Map<String, Object>> arr = (List<Map<String, Object>>) new Gson().fromJson(response, Map.class).get("results");
            if (arr != null && arr.size() > 0) {
                for (Map<String, Object> node : arr) {
                    a++;
                    mark += "   ";
                    String orgName = (String) node.get("orgName");
                    String orgId = (String) node.get("id");

                    System.out.println(mark + a + "级组织为: " + orgName + " = (组织编码)" + orgId);
                    totalOrganizations++;

                    // 获取当前组织的通道信息
                    getOrgDevTree(orgId, orgName, mark, token);

                    // 递归获取子组织
                    getSub(orgId, token);

                    // 回退层级标记
                    mark = mark.substring(0, mark.length() - 3);
                    a--;
                }
            }
        } catch (Exception e) {
            System.err.println(mark + "❌ 解析组织信息失败: " + e.getMessage());
        }
    }

    /**
     * 基于DevInfo.getOrgDevTree方法的获取组织通道信息
     * 此方法为获取组织和设备树专用方法
     */
    public static void getOrgDevTree(String orgCode, String orgName, String mark, String token) throws Exception {
        try {
            String content = "?id=" + orgCode + "&nodeType=1&typeCode=01;0;ALL;ALL&page=1&pageSize=100";
            String response = sendSecureHttpsRequest("GET", ORG_TREE_ACTION, token, content);

            if (response == null || !response.startsWith("{")) {
                return;
            }

            Map<String, Object> rsp = new Gson().fromJson(response, Map.class);
            List<Map<String, Object>> arr = (List<Map<String, Object>>) rsp.get("results");

            int start = 0;
            int total = 0;

            if (arr != null && arr.size() > 0) {
                // 先统计通道数量
                for (Map<String, Object> node : arr) {
                    DecimalFormat df = new DecimalFormat("######0");
                    if (3 == Integer.valueOf(df.format(node.get("nodeType")))) {
                        start++;
                    }
                }

                // 如果有通道，显示通道信息
                if (start > 0) {
                    System.out.print("            " + mark);
                }

                for (Map<String, Object> node : arr) {
                    DecimalFormat df = new DecimalFormat("######0");
                    if (3 == Integer.valueOf(df.format(node.get("nodeType")))) {
                        total++;
                        String channelName = (String) node.get("channelName");
                        String channelId = (String) node.get("channelId");
                        System.out.print("（通道名称）" + channelName + " = （通道编码）" + channelId + "; ");

                        // 收集通道信息
                        allChannels.add(new ChannelInfo(channelId, channelName, orgName, orgCode, a));
                        totalChannels++;
                    }
                }

                if (total > 0) {
                    System.out.println();
                }
            }
        } catch (Exception e) {
            System.err.println(mark + "❌ 获取通道信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个通道指定协议的实时监控URI（HTTPS版本）
     * @param channelId 通道ID
     * @param scheme 协议类型 (RTSP, FLV_HTTP, HLS)
     * @param subType 码流类型 (0:主码流、1:辅流1、2:辅流2)
     * @param token 认证Token
     * @return 监控URL
     */
    private static String getRealMonitorUrl(String channelId, String scheme, int subType, String token) throws Exception {
        /**
         * channelId : 类型string ，必填。通道编码。
         * subType : 类型int ，选填。码流类型，0:主码流、1:辅流1、2:辅流2。默认为0主码流。
         * scheme : 类型string ，选填。协议类型，支持RTSP、FLV_HTTP、HLS三种，默认RTSP。
         */
        String content = "?channelId=" + channelId +
                        "&scheme=" + scheme +
                        "&subType=" + subType;

        String response = sendSecureHttpsRequest("GET", REAL_MONITOR_ACTION, token, content);

        if (response == null || response.trim().isEmpty()) {
            throw new Exception("收到空响应");
        }

        // 检查响应是否为有效JSON
        if (!response.startsWith("{")) {
            throw new Exception("收到非JSON响应: " + response);
        }

        try {
            Map<String, Object> responseMap = new Gson().fromJson(response, Map.class);
            Object urlObj = responseMap.get("url");

            if (urlObj != null) {
                return urlObj.toString();
            } else {
                throw new Exception("响应中未找到URL字段");
            }
        } catch (Exception e) {
            throw new Exception("解析响应JSON失败: " + e.getMessage());
        }
    }

    /**
     * 为所有通道获取实时监控URI
     */
    private static void getAllChannelsMonitorUrls(String token) {
        System.out.println("\n🎥 开始为所有通道获取实时监控URI...");
        System.out.println("支持的协议: " + String.join(", ", SUPPORTED_SCHEMES));
        System.out.println();

        if (allChannels.isEmpty()) {
            System.out.println("❌ 没有找到任何通道信息");
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (ChannelInfo channel : allChannels) {
            System.out.println("📹 处理通道: " + channel.channelName + " (ID: " + channel.channelId + ")");

            for (String scheme : SUPPORTED_SCHEMES) {
                try {
                    String url = getRealMonitorUrl(channel.channelId, scheme, 0, token);
                    if (url != null && !url.trim().isEmpty()) {
                        MonitorUrlInfo urlInfo = new MonitorUrlInfo(
                            channel.channelId,
                            channel.channelName,
                            channel.orgName,
                            scheme,
                            url,
                            0
                        );
                        allMonitorUrls.add(urlInfo);
                        System.out.println("   ✅ " + scheme + ": " + url);
                        successCount++;
                        totalMonitorUrls++;
                    } else {
                        System.out.println("   ❌ " + scheme + ": 获取到空URL");
                        failCount++;
                    }
                } catch (Exception e) {
                    System.out.println("   ❌ " + scheme + ": " + e.getMessage());
                    failCount++;
                }
            }
            System.out.println();
        }

        System.out.println("📊 监控URI获取统计:");
        System.out.println("   ✅ 成功获取: " + successCount + " 个");
        System.out.println("   ❌ 获取失败: " + failCount + " 个");
        System.out.println("   📹 总通道数: " + allChannels.size());
        System.out.println("   🎯 总URI数: " + totalMonitorUrls);
    }

    /**
     * 打印详细结果
     */
    private static void printDetailedResults() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("                    📊 详细结果报告");
        System.out.println("=".repeat(80));

        // 按组织分组显示
        Map<String, List<MonitorUrlInfo>> orgGroups = new HashMap<>();
        for (MonitorUrlInfo urlInfo : allMonitorUrls) {
            orgGroups.computeIfAbsent(urlInfo.orgName, k -> new ArrayList<>()).add(urlInfo);
        }

        for (Map.Entry<String, List<MonitorUrlInfo>> entry : orgGroups.entrySet()) {
            String orgName = entry.getKey();
            List<MonitorUrlInfo> urls = entry.getValue();

            System.out.println("\n📁 组织: " + orgName);
            System.out.println("   包含 " + urls.size() + " 个监控URI");

            // 按通道分组
            Map<String, List<MonitorUrlInfo>> channelGroups = new HashMap<>();
            for (MonitorUrlInfo url : urls) {
                channelGroups.computeIfAbsent(url.channelId, k -> new ArrayList<>()).add(url);
            }

            for (Map.Entry<String, List<MonitorUrlInfo>> channelEntry : channelGroups.entrySet()) {
                List<MonitorUrlInfo> channelUrls = channelEntry.getValue();
                if (!channelUrls.isEmpty()) {
                    MonitorUrlInfo firstUrl = channelUrls.get(0);
                    System.out.println("   📹 通道: " + firstUrl.channelName + " (ID: " + firstUrl.channelId + ")");

                    for (MonitorUrlInfo url : channelUrls) {
                        System.out.println("      🔗 " + url.scheme + ": " + url.url);
                    }
                }
            }
        }

        System.out.println("\n" + "=".repeat(80));
        System.out.println("📈 最终统计信息:");
        System.out.println("   📁 总组织数: " + totalOrganizations);
        System.out.println("   📹 总通道数: " + totalChannels);
        System.out.println("   🎯 总监控URI数: " + totalMonitorUrls);
        System.out.println("   🔗 支持协议: " + String.join(", ", SUPPORTED_SCHEMES));
        System.out.println("=".repeat(80));
    }

    /**
     * 运行完整的工作流程（集成会话管理功能）
     */
    public static void runCompleteWorkflow() {
        try {
            // 添加关闭钩子确保程序异常退出时也能正确清理
            addShutdownHook();

            // 重置统计信息和数据
            totalOrganizations = 0;
            totalChannels = 0;
            totalMonitorUrls = 0;
            allChannels.clear();
            allMonitorUrls.clear();
            a = -1;
            mark = "";

            System.out.println("=".repeat(80));
            System.out.println("        🎥 实时监控通道管理器 - 完整融合版");
            System.out.println("=".repeat(80));
            System.out.println("🔒 服务器地址: " + ip + ":" + port + " (安全HTTPS)");
            System.out.println("👤 用户名: " + userName);
            System.out.println("📜 使用证书: rootChain.crt + key.cer");
            System.out.println("🎯 功能: 获取所有通道的实时监控URI");
            System.out.println("🔗 支持协议: " + String.join(", ", SUPPORTED_SCHEMES));
            System.out.println("🔄 会话管理: 自动保活 + 优雅登出");
            System.out.println();

            // 步骤1: 自动登录获取Token
            System.out.println("步骤1: 🔑 自动SSL证书登录获取Token...");
            String token = performSecureLogin();
            System.out.println("Token: " + token.substring(0, Math.min(30, token.length())) + "...");

            // 步骤1.5: 启动自动保活（融合KeepLogin功能）
            System.out.println("\n步骤1.5: 🔄 启动自动会话保活...");
            startKeepAlive(token);
            System.out.println();

            // 步骤2: 递归查询组织树和通道信息
            System.out.println("步骤2: 📁 递归查询组织树结构和通道信息...");
            System.out.println();

            getSub("", token);

            System.out.println("\n📊 组织和通道查询完成:");
            System.out.println("   📁 找到组织数: " + totalOrganizations);
            System.out.println("   📹 找到通道数: " + totalChannels);

            if (totalChannels == 0) {
                System.out.println("❌ 未找到任何通道，程序结束");
                return;
            }

            // 步骤3: 为所有通道获取实时监控URI
            System.out.println("\n步骤3: 🎥 为所有通道获取实时监控URI...");
            getAllChannelsMonitorUrls(token);

            // 步骤4: 显示详细结果
            System.out.println("\n步骤4: 📋 生成详细结果报告...");
            printDetailedResults();

            System.out.println("\n🎉 实时监控通道管理器执行完成！");
            System.out.println("✅ 所有通道的实时监控URI已成功获取并打印");

            // 步骤5: 执行完整登出流程（融合LoginOut功能）
            System.out.println("\n步骤5: 🔄 执行完整登出流程...");
            performCompleteLogout();
            System.out.println("✅ 程序正常结束");

        } catch (Exception e) {
            System.err.println("❌ 工作流程执行失败: " + e.getMessage());
            System.err.println("\n🔧 故障排除建议:");
            System.err.println("1. 检查SSL证书文件是否存在且有效 (rootChain.crt, key.cer)");
            System.err.println("2. 确认服务器地址和端口配置正确");
            System.err.println("3. 检查用户名和密码是否正确");
            System.err.println("4. 确认网络连接状态和HTTPS访问权限");
            System.err.println("5. 检查Token是否有效且具有相应权限");

            // 即使出现异常也要尝试登出
            System.err.println("\n🔄 尝试执行清理操作...");
            try {
                performCompleteLogout();
            } catch (Exception logoutException) {
                System.err.println("⚠️ 清理操作也失败了: " + logoutException.getMessage());
            }

            e.printStackTrace();
        }
    }

    /**
     * 显示使用帮助
     */
    private static void showHelp() {
        System.out.println("=".repeat(80));
        System.out.println("              🎥 实时监控通道管理器 - 使用帮助");
        System.out.println("=".repeat(80));
        System.out.println();
        System.out.println("📋 功能说明：");
        System.out.println("   本程序融合了ComprehensiveOrgChannelManager、RealMonitor、");
        System.out.println("   KeepLogin和LoginOut的完整功能");
        System.out.println("   使用HTTPS SSL证书安全连接，获取所有通道的实时监控URI");
        System.out.println("   并提供完整的会话生命周期管理");
        System.out.println();
        System.out.println("🔧 主要特性：");
        System.out.println("   ✅ SSL证书安全认证");
        System.out.println("   ✅ 自动登录获取Token");
        System.out.println("   ✅ 自动会话保活（110秒间隔）");
        System.out.println("   ✅ 优雅的会话登出和清理");
        System.out.println("   ✅ 递归查询完整组织机构树");
        System.out.println("   ✅ 获取所有通道信息");
        System.out.println("   ✅ 支持多种协议 (RTSP, FLV_HTTP, HLS)");
        System.out.println("   ✅ 详细结果报告和统计");
        System.out.println("   ✅ 异常退出时自动清理资源");
        System.out.println();
        System.out.println("📁 必需文件：");
        System.out.println("   - src/main/resources/baseinfo.properties (配置文件)");
        System.out.println("   - src/main/resources/rootChain.crt (根证书)");
        System.out.println("   - src/main/resources/key.cer (服务器证书)");
        System.out.println();
        System.out.println("🎯 输出内容：");
        System.out.println("   - 📁 完整的组织机构树形结构");
        System.out.println("   - 📹 每个组织包含的通道信息");
        System.out.println("   - 🎥 每个通道对应的实时监控URI");
        System.out.println("   - 📊 详细统计信息");
        System.out.println("   - 🔄 会话保活状态信息");
        System.out.println("   - ✅ 完整的登出清理日志");
        System.out.println();
        System.out.println("🔄 会话管理：");
        System.out.println("   - 自动启动保活线程（110秒间隔）");
        System.out.println("   - 程序结束时自动登出");
        System.out.println("   - 异常退出时通过关闭钩子清理资源");
        System.out.println("   - 自动清空token配置文件");
        System.out.println();
        System.out.println("=".repeat(80));
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        if (args.length > 0 && "help".equals(args[0])) {
            showHelp();
            return;
        }

        System.out.println("🚀 启动实时监控通道管理器...");
        System.out.println("💡 融合ComprehensiveOrgChannelManager + RealMonitor + KeepLogin + LoginOut功能");
        System.out.println("🔒 全程使用HTTPS安全连接");
        System.out.println("🔄 集成完整会话生命周期管理");
        System.out.println();

        runCompleteWorkflow();
    }
}
