# 实时监控通道管理器 (RealMonitorChannelManager)

## 概述

`RealMonitorChannelManager` 是一个融合了 `ComprehensiveOrgChannelManager.java` 和 `RealMonitor.java` 功能的综合管理器。它能够：

1. 使用SSL证书进行HTTPS安全连接
2. 自动登录获取Token
3. 递归查询完整的组织机构树结构
4. 查询每个组织包含的所有通道信息
5. 为每个通道获取实时监控URI（支持RTSP、FLV_HTTP、HLS协议）
6. 全程使用HTTPS访问，确保安全性

## 主要特性

### ✅ 安全性
- 使用SSL证书进行HTTPS连接
- 支持大华平台的根证书和服务器证书
- MD5签名认证机制

### ✅ 完整性
- 递归获取所有组织机构树
- 获取每个组织的所有通道信息
- 为每个通道获取多种协议的监控URI

### ✅ 协议支持
- RTSP协议
- FLV_HTTP协议  
- HLS协议

### ✅ 用户友好
- 详细的进度显示
- 美观的结果报告
- 完整的统计信息
- 错误处理和故障排除建议

## 使用方法

### 前置条件

确保以下文件存在：
- `src/main/resources/baseinfo.properties` (配置文件)
- `src/main/resources/rootChain.crt` (根证书)
- `src/main/resources/key.cer` (服务器证书)

### 运行方式

```bash
# 运行主程序
java com.dahua.demo.basic.RealMonitorChannelManager

# 显示帮助信息
java com.dahua.demo.basic.RealMonitorChannelManager help
```

### 输出示例

```
================================================================================
        🎥 实时监控通道管理器 - 融合版
================================================================================
🔒 服务器地址: 192.168.1.100:443 (安全HTTPS)
👤 用户名: admin
📜 使用证书: rootChain.crt + key.cer
🎯 功能: 获取所有通道的实时监控URI
🔗 支持协议: RTSP, FLV_HTTP, HLS

步骤1: 🔑 自动SSL证书登录获取Token...
🔐 正在加载SSL证书...
✅ 根证书加载成功
✅ 服务器证书加载成功
🔒 SSL上下文初始化完成
✅ 第一次登录成功，获取到认证信息
✅ 登录成功，Token已获取
Token: abcd1234567890abcd1234567890...

步骤2: 📁 递归查询组织树结构和通道信息...

0级组织为: 总部 = (组织编码)org001
            （通道名称）前门摄像头 = （通道编码）ch001; （通道名称）后门摄像头 = （通道编码）ch002; 
   1级组织为: 分公司A = (组织编码)org002
               （通道名称）大厅摄像头 = （通道编码）ch003; 

📊 组织和通道查询完成:
   📁 找到组织数: 2
   📹 找到通道数: 3

步骤3: 🎥 为所有通道获取实时监控URI...
支持的协议: RTSP, FLV_HTTP, HLS

📹 处理通道: 前门摄像头 (ID: ch001)
   ✅ RTSP: rtsp://192.168.1.100:554/live/ch001
   ✅ FLV_HTTP: http://192.168.1.100:8080/live/ch001.flv
   ✅ HLS: http://192.168.1.100:8080/live/ch001.m3u8

📹 处理通道: 后门摄像头 (ID: ch002)
   ✅ RTSP: rtsp://192.168.1.100:554/live/ch002
   ✅ FLV_HTTP: http://192.168.1.100:8080/live/ch002.flv
   ✅ HLS: http://192.168.1.100:8080/live/ch002.m3u8

📊 监控URI获取统计:
   ✅ 成功获取: 9 个
   ❌ 获取失败: 0 个
   📹 总通道数: 3
   🎯 总URI数: 9

步骤4: 📋 生成详细结果报告...

================================================================================
                    📊 详细结果报告
================================================================================

📁 组织: 总部
   包含 6 个监控URI
   📹 通道: 前门摄像头 (ID: ch001)
      🔗 RTSP: rtsp://192.168.1.100:554/live/ch001
      🔗 FLV_HTTP: http://192.168.1.100:8080/live/ch001.flv
      🔗 HLS: http://192.168.1.100:8080/live/ch001.m3u8
   📹 通道: 后门摄像头 (ID: ch002)
      🔗 RTSP: rtsp://192.168.1.100:554/live/ch002
      🔗 FLV_HTTP: http://192.168.1.100:8080/live/ch002.flv
      🔗 HLS: http://192.168.1.100:8080/live/ch002.m3u8

📁 组织: 分公司A
   包含 3 个监控URI
   📹 通道: 大厅摄像头 (ID: ch003)
      🔗 RTSP: rtsp://192.168.1.100:554/live/ch003
      🔗 FLV_HTTP: http://192.168.1.100:8080/live/ch003.flv
      🔗 HLS: http://192.168.1.100:8080/live/ch003.m3u8

================================================================================
📈 最终统计信息:
   📁 总组织数: 2
   📹 总通道数: 3
   🎯 总监控URI数: 9
   🔗 支持协议: RTSP, FLV_HTTP, HLS
================================================================================

🎉 实时监控通道管理器执行完成！
✅ 所有通道的实时监控URI已成功获取并打印
```

## 技术实现

### 核心方法

1. **initSSLContext()** - 初始化SSL证书上下文
2. **sendSecureHttpsRequest()** - 发送安全HTTPS请求
3. **performSecureLogin()** - 执行SSL证书登录
4. **getSub()** - 递归获取组织机构树
5. **getOrgDevTree()** - 获取组织通道信息
6. **getRealMonitorUrl()** - 获取实时监控URI
7. **getAllChannelsMonitorUrls()** - 批量获取所有通道的监控URI

### 数据模型

- **ChannelInfo** - 通道信息类
- **MonitorUrlInfo** - 监控URL信息类

## 故障排除

如果遇到问题，请检查：

1. SSL证书文件是否存在且有效 (rootChain.crt, key.cer)
2. 服务器地址和端口配置是否正确
3. 用户名和密码是否正确
4. 网络连接状态和HTTPS访问权限
5. Token是否有效且具有相应权限

## 与原始文件的对比

### 相比 ComprehensiveOrgChannelManager.java
- ✅ 保留了所有SSL证书和HTTPS功能
- ✅ 保留了组织机构树查询功能
- ➕ 新增了实时监控URI获取功能

### 相比 RealMonitor.java  
- ✅ 保留了实时监控URI获取核心功能
- ➕ 改进为HTTPS安全连接
- ➕ 支持批量处理多个通道
- ➕ 支持多种协议类型

## 总结

`RealMonitorChannelManager` 成功融合了两个原始文件的功能，提供了一个完整的、安全的、用户友好的实时监控通道管理解决方案。它使用HTTPS连接确保安全性，能够获取所有通道的实时监控URI并以美观的格式展示结果。
